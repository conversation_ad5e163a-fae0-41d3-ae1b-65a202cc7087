const {WeaDateGroup, WeaBrowser, WeaLocaleProvider} = ecCom
const {Button} = antd;
const {getLabel} = WeaLocaleProvider;

const img2Props = {
    src: "/cloudstore/release/${appId}/resources/xiaoshou_report.png",
    height: 32,
};

//读取config
const config = ecodeSDK.getCom("${appId}", "config");

class Main extends React.Component {
    constructor(props) {
        super(props);
        let currentYear = new Date().getFullYear() + "";
        this.state = {
            searchSort: "1",//默认按照时间正序查询数据，0正序，1倒序
            buttonAscLoading: false,
            buttonLoading: false,
            currentYear: currentYear,
            khmc: "",
            reporter: "",
            startDate: "",
            endDate: "",
            dataGroupValue: ["-1"], //默认全部
            kh_viewAttr: 2,
            fixed_khid: "",//固定客户id
            fixed_khmc: "",//固定客户名称
            fixed_manager: "",//固定客户经理id
            fixed_manager_name: "",//固定客户经理名称
            fixed_hyczname: "",//固定行业产轴
            fixed_display: "none",
            normal_display: "flex",
        }
    }

    componentWillMount() {
    }

    componentDidMount() {
        let that = this;
        //判断是否有url参数
        const {util} = window.GRSDK
        let kh = util.getBrowserUrlParam("kh");
        let hidetitle = util.getBrowserUrlParam("hidetitle");

        let khname = "";
        let customerData = [];
        let kh_viewAttr = 2;
        let display_title = "flex";
        if (hidetitle && hidetitle + "" === "1") {
            display_title = "none";
        }
        if (kh) {
            khname = this.getCustomerName(kh);
            kh_viewAttr = 1;
            customerData = [{
                id: kh,
                name: khname,
            }]
        }
        this.setState({
            kh_viewAttr: kh_viewAttr,
            khmc: kh,
            display_title: display_title,
            customerData: customerData
        }, () => {
            that.refreshAll();
        })
    }

    getCustomerName = (kh) => {
        const {db} = window.GRSDK;
        let sql = "select name from crm_customerinfo where id =" + kh;
        let result = db.query(sql);
        if (result && result.data && result.data.length > 0) {
            return result.data[0].name;
        }
        return "";
    }

    /**
     * 设置子页面的ref，将子页面的this传递过来，方便当前页面调用
     * @param name
     * @returns {(function(*): void)|*}
     */
    setChildRef = (name) => (ref) => {
        this[name] = ref;
    }


    refreshAll = () => {
        const {khmc, reporter, startDate, endDate, searchSort} = this.state;
        console.log("refreshAll", startDate, endDate);
        if (this.content) {
            this.content.refresh(khmc, reporter, startDate, endDate, searchSort);
        }
    }

    /**
     * 正序查看搜索
     */
    clickRefreshAsc = () => {
        let that = this;
        this.setState({
            buttonAscLoading: true,
            searchSort: "0"
        }, () => {
            that.refreshAll();
            this.setState({buttonAscLoading: false});
        });
    }

    /**
     * 倒序搜索
     */
    clickRefresh = () => {
        let that = this;
        this.setState({
            buttonLoading: true,
            searchSort: "1"
        }, () => {
            that.refreshAll();
            this.setState({buttonLoading: false});
        });
    }

    /**
     * 选择提交人
     * @param ids
     * @param names
     * @param datas
     */
    selectPerson = (ids, names, datas) => {
        let that = this;
        console.log("选择提交人：", datas);
        this.setState({
            reporter: ids,
        }, () => {
            that.refreshAll();
        })
    }
    /**
     * 选择客户
     * @param ids
     * @param names
     * @param datas
     */
    selectCustomer = (ids, names, datas) => {
        let that = this;
        console.log("选择客户：", datas);
        this.setState({
            khmc: ids,
            customerData: datas
        }, () => {
            that.refreshAll();
        })
    }
    /**
     * 选择日期范围
     * @param value
     */
    selectDateGroup = (value) => {
        let that = this;
        console.log("选择日期范围", value);
        let dataRange = util.getDateRangeByValue(value);
        console.log("选择日期范围解析", dataRange);

        this.setState({
            dataGroupValue: value,
            startDate: dataRange.startDate,
            endDate: dataRange.endDate,
        }, () => {
            //如果没有解析出来范围，则不刷新
            //选择范围的时候
            if (value[0] + "" === "6") {
                if (dataRange.startDate && dataRange.endDate) {
                    that.refreshAll();
                }
            } else {
                that.refreshAll();
            }

        })
    }

    setFixedBar = (params) => {
        this.setState({
            normal_display: "none",
            fixed_display: "flex",
            fixed_khid: params.fixed_khid,
            fixed_khmc: params.fixed_khmc,
            fixed_manager: params.fixed_manager,
            fixed_manager_name: params.fixed_manager_name,
            fixed_hyczname: params.fixed_hyczname,
        })
    }

    /**
     * 渲染
     *
     */
    render() {
        const {
            dataGroupValue, buttonAscLoading, buttonLoading, customerData, kh_viewAttr,
            fixed_display, normal_display,
            fixed_khid, fixed_khmc, fixed_manager, fixed_manager_name, fixed_hyczname,
            display_title
        } = this.state;
        return (
            <div className={"SD_Page"}>
                <div className={"top-toolbar"}>
                    <div className={"title"} style={{
                        display: display_title
                    }}>
                        <img {...img2Props} />
                        <span>销售报告</span>
                    </div>
                    <div className={"search"}>
                        <div className="condition">
                            <div style={{
                                display: normal_display,
                                alignItems: "center"
                            }}>
                             <span className={"searchName"}>
                            客户
                        </span>
                                <WeaBrowser
                                    type={161}
                                    title="选择客户"
                                    completeParams={{type: 161, fielddbtype: config.browser_customer}}
                                    conditionDataParams={{type: config.browser_customer}}
                                    dataParams={{
                                        type: config.browser_customer,
                                    }}
                                    destDataParams={{type: config.browser_customer}}
                                    {...defaultBrowserParams}
                                    inputStyle={{width: 120}}
                                    style={{
                                        background: "white",
                                    }}
                                    replaceDatas={customerData}
                                    linkUrl="/spa/crm/static/index.html#/main/crm/customerView?customerId="
                                    viewAttr={kh_viewAttr}
                                    onChange={this.selectCustomer}

                                />
                            </div>

                            <span className={"searchName"}>
                            提交人
                        </span>
                            <WeaBrowser
                                type={1}
                                title="人力资源"
                                tabs={[
                                    {
                                        name: getLabel(24515, "最近"),
                                        key: "1"
                                    },
                                    {
                                        name: getLabel(18511, "同部门"),
                                        key: "2"
                                    },
                                    {
                                        name: getLabel(15089, "我的下属"),
                                        key: "3"
                                    },
                                    {
                                        name: getLabel(18770, "按组织结构"),
                                        key: "4",
                                        browserProps: {
                                            browserTreeCustomProps: {
                                                defaultExpandedLevel: 2
                                            }
                                        }
                                    },
                                    {
                                        name: getLabel(81554, "常用组"),
                                        key: "5"
                                    },
                                    {
                                        name: "所有人",
                                        key: "6"
                                    }
                                ]}
                                showDls
                                linkUrl="/spa/hrm/index_mobx.html#/main/hrm/card/cardInfo/"
                                isSingle={true}
                                inputStyle={{width: 120}}
                                onChange={this.selectPerson}
                            />
                            <span className={"searchName"}>
                            报告日期
                        </span>
                            <WeaDateGroup
                                isMobx
                                isInline
                                value={dataGroupValue}
                                datas={dataGroupDatas}
                                onChange={this.selectDateGroup}
                                style={{
                                    width: "120px",
                                    background: "white",
                                    marginLeft: "10px"
                                }}
                            />
                        </div>
                        <div className={"button_area"}>
                            <Button type="primary" icon="reload" loading={buttonAscLoading}
                                    onClick={this.clickRefreshAsc}>
                                正序查看
                            </Button>
                            <Button type="primary" icon="reload" loading={buttonLoading}
                                    onClick={this.clickRefresh}>
                                搜索
                            </Button>
                        </div>

                    </div>
                </div>
                <div className={"fixed_field_bar"} style={{
                    display: fixed_display
                }}>
                    <div className="fixed_field">
                        <span className="label">客户名称：</span>
                        <span className="value">
                                <a
                                    href={`/spa/crm/static/index.html#/main/crm/customerView?customerId=${fixed_khid}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    {fixed_khmc}
                                </a>
                            </span>
                    </div>
                    <div className="fixed_field">
                        <span className="label">客户经理：</span>
                        <span className="value">
                                <WeaBrowser
                                    type={1}
                                    title="人力资源"
                                    showDls
                                    viewAttr={1}
                                    replaceDatas={[{id: fixed_manager, name: fixed_manager_name}]}
                                    linkUrl="/spa/hrm/index_mobx.html#/main/hrm/card/cardInfo/"
                                    {...defaultBrowserParams}
                                />
                            </span>
                    </div>
                    <div className="fixed_field">
                        <span className="label">行业-产轴：</span>
                        <span className="value">{fixed_hyczname}</span>
                    </div>

                </div>
                <Content {...this.props} setPageRef={this.setChildRef("content")} setFixedBar={this.setFixedBar}/>

            </div>

        )
    }
}
