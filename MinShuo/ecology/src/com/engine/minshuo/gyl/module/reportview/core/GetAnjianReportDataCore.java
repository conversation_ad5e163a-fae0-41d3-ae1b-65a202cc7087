package com.engine.minshuo.gyl.module.reportview.core;

import com.engine.minshuo.gyl.module.reportview.core.ext.GetAnjianReportDataCoreExt;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.doc.dto.DocFileInfo;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.sd2.functionlog.bean.SDLog;
import com.engine.sd2.functionlog.util.SDLogUtil;
import com.engine.sd2.hrm.util.HrmUtil;
import com.engine.sd2.module.modquery.ModQueryUtil;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.*;

/**
 * @FileName GetAnjianReportDataCore.java
 * @Description 获取报告视图数据-案件报告
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/7/10
 */
public class GetAnjianReportDataCore extends GetAnjianReportDataCoreExt {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public GetAnjianReportDataCore(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    /**
     * 初始化
     */
    private void init() {
        try {
            log.info(this.getClass().getName() + "---START---params:" + params);
            // 初始化日志打印工具类
            sdLogUtil = new SDLogUtil();
            // 初始化日志bean
            sdLog = new SDLog(user.getUID(),
                    this.getClass().getSimpleName(),
                    this.getClass().getName(),
                    SDLog.TYPE_API,
                    "获取报告数据-案件报告");
            sdLog.setRelate_module("报告");
        } catch (Exception e) {
            error = "初始化步骤异常:" + SDUtil.getExceptionDetail(e);
            log.error("初始化步骤异常", e);
        }
    }

    /**
     * 执行
     *
     * @return
     */
    public Map<String, Object> execute() {
        try {
            // 初始化
            init();
            result.put("dataKey", Util.null2String(params.get("dataKey")));//先默认返回接口传进来的dataKey
            if (error.isEmpty()) {
                // 校验参数
                checkParam();
            }
            if (error.isEmpty()) {
                // 获取有权限的数据id列表，经过查询条件筛选后的
                getAuthIds();
                if (!authIds.isEmpty()) {
                    // 构建数据
                    buildResultData();
                } else {
                    appendLog("未查询到有权限的报告数据id");
                }

            }
        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        } finally {
            appendLog(this.getClass().getName() + "---END");
            // 后置执行操作
            afterExecute();
        }
        result.put("error", error);
        result.put("status", error.isEmpty());
        result.put("data", reportData);
        log.info(this.getClass().getName() + "---END---");
        return result;
    }

    /**
     * 构建数据
     */
    private void buildResultData() {
        // 获取有权限的相关数据
        getData();
        if (reportData != null && !reportData.isEmpty()) {
            //处理特殊字段数据
            handleField_hycz();
            //产品品牌
            handleField_cppp();
            //产品经理
            handleField_cpjl();
            //附件
            attachDocInfoToReportData();
            //评论数据
            handleCommentData();
        } else {
            appendLog("筛选权限后无报告数据");
        }
    }

    /**
     * 处理评论数据
     */
    private void handleCommentData() {
        // 预分组，将评论数据，分组做成map
        Map<String, List<Map<String, Object>>> commentMap = new HashMap<>();
        for (Map<String, Object> comment : commentData) {
            String rqid = Util.null2String(comment.get("rqid"));
            commentMap.computeIfAbsent(rqid, k -> new ArrayList<>()).add(comment);
        }
        // 3、循环reportData，组装描述数据和评论数据
        for (Map<String, Object> main : reportData) {
            String mainId = Util.null2String(main.get("id"));
            main.put("commentList", commentMap.getOrDefault(mainId, new ArrayList<>()));
        }
    }

    /**
     * 给主数据组装文档信息
     */
    private void attachDocInfoToReportData() {
        Set<String> docIdSet = new HashSet<>();
        for (Map<String, Object> main : reportData) {
            String docids = Util.null2String(main.get("fj"));
            if (!docids.isEmpty()) {
                for (String id : docids.split(CommonCst.COMMA_EN)) {
                    id = id.trim();
                    if (!id.isEmpty()) {
                        docIdSet.add(id);
                    }
                }
            }
        }
        String allDocIds = String.join(",", docIdSet);
        List<DocFileInfo> docInfoList = new ArrayList<>();
        if (!allDocIds.isEmpty()) {
            docInfoList = DocUtil.getDocFileInfoByDocIds(allDocIds);
        }
        Map<String, DocFileInfo> docInfoMap = new HashMap<>();
        for (DocFileInfo doc : docInfoList) {
            docInfoMap.put(String.valueOf(doc.getDocid()), doc);
        }
        for (Map<String, Object> main : reportData) {
            String docids = Util.null2String(main.get("fj"));
            List<DocFileInfo> docList = new ArrayList<>();
            if (!docids.isEmpty()) {
                for (String id : docids.split(CommonCst.COMMA_EN)) {
                    id = id.trim();
                    if (!id.isEmpty() && docInfoMap.containsKey(id)) {
                        docList.add(docInfoMap.get(id));
                    }
                }
            }
            main.put("docList", docList);
        }
    }

    /**
     * 获取有权限的数据
     */
    private void getAuthIds() {
        String customid = Util.null2String(params.get("auth_customid")); // 权限建模查询id
        String cookie = Util.null2String(params.get("request_header_cookie")); // cookie
        String port = Util.null2String(params.get("port")); // 内部服务器的port端口号
        String xm = Util.null2String(params.get("xm")); // 搜索条件-项目
        String bgr = Util.null2String(params.get("bgr")); // 搜索条件-报告人
        String startdate = Util.null2String(params.get("startdate")); // 搜索条件-开始日期
        String enddate = Util.null2String(params.get("enddate")); // 搜索条件-结束日期

        String xm_paramname = Util.null2String(params.get("xm_paramname")); // 建模搜索条件字段id-项目-对应权限建模查询的参数名
        String bgr_paramname = Util.null2String(params.get("bgr_paramname")); // 建模搜索条件字段id-报告人-对应权限建模查询的参数名
        String bgrq_paramname = Util.null2String(params.get("bgrq_paramname")); // 建模搜索条件字段id-日期范围-对应权限建模查询的参数名

        String dataKey = Util.null2String(params.get("dataKey"));//权限建模查询的dataKey，首次查询肯定是空的，后续分页时，可以从前端传入
        int pageNo = Util.getIntValue(Util.null2String(params.get("pageNo")), 1);//分页页码

        Map<String, Object> params = new HashMap<>();
        //项目
        if (!xm.isEmpty()) {
            params.put(xm_paramname, xm);
        }
        //报告人
        if (!bgr.isEmpty()) {
            params.put(bgr_paramname, bgr);
        } else {
            params.put(bgr_paramname, "-5,");
        }
        //报告日期范围
        if (!startdate.isEmpty() && !enddate.isEmpty()) {
            String dateParam = "6," + startdate + "," + enddate;
            params.put(bgrq_paramname, dateParam);
        } else {
            params.put(bgrq_paramname, "0,,");
        }
        appendLog("建模查询权限参数:" + params);
        //先获取datakey
        if (dataKey.isEmpty()) {
            dataKey = ModQueryUtil.getDataKey(port, customid, cookie, params);
        }
        //获取建模查询结构
        ModQueryUtil.ModQueryDataResult dataResult = ModQueryUtil.getPageDataListWithDatakey(port, customid, cookie, params, pageNo, dataKey);
        List<Map<String, Object>> dataList = dataResult.getList();
        //这里将最新的dataKey返回到前端
        result.put("dataKey", Util.null2String(dataResult.getDataKey()));
        log.info("auth dataList dataKey:" + Util.null2String(dataResult.getDataKey()));
        if (dataList != null && !dataList.isEmpty()) {
            log.info("auth dataList:" + dataList);
            for (Map<String, Object> map : dataList) {
                authIds.add(Util.null2String(map.get("id")));
            }
        }
    }

    /**
     * 获取相关数据
     */
    private void getData() {
        String sql;
        List<Map<String, Object>> batchData = null;
        try {
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            List<String> authIdList = new ArrayList<>(authIds);
            // 确保每次查询前清空
            reportData = new ArrayList<>();
            commentData = new ArrayList<>();
            // 按批次获取，1000个一批次
            for (int i = 0; i < authIdList.size(); i += batchSize) {
                List<String> batch = authIdList.subList(i, Math.min(i + batchSize, authIdList.size()));
                String batchAuthIds = String.join(",", batch);
                // 1、获取主数据
                sql = getMainSql(batchAuthIds);
                appendLog("查询报告主数据sql:" + sql);
                if (rs.executeQuery(sql)) {
                    batchData = SDUtil.lowerMapKey(QueryUtil.getMapList(rs));
                    if (!batchData.isEmpty()) {
                        reportData.addAll(batchData);
                    }
                } else {
                    error = "查询报告主数据sql出错:" + rs.getExceptionMsg();
                    appendLog(error);
                }
                //如果主数据都没有获取到，则不获取附属数据
                if (batchData != null && !batchData.isEmpty()) {
                    // 获取评论数据
                    sql = getDetailcommentSql(batchAuthIds);
                    appendLog("查询评论数据sql:" + sql);
                    if (rs.executeQuery(sql)) {
                        batchData = SDUtil.lowerMapKey(QueryUtil.getMapList(rs));
                        if (!batchData.isEmpty()) {
                            commentData.addAll(batchData);
                        }
                    } else {
                        error = "查询评论数据sql出错:" + rs.getExceptionMsg();
                        appendLog(error);
                    }
                }
            }
            appendLog("报告主数据 大小：" + reportData.size());
            appendLog("评论数据 大小：" + commentData.size());

        } catch (Exception e) {
            appendLog("获取数据异常：" + SDUtil.getExceptionDetail(e));
            log.error("获取数据异常：", e);
        }

    }

    /**
     * 报告主数据sql
     *
     * @param batchAuthIds
     * @return
     */
    private String getMainSql(String batchAuthIds) {
        String searchSort = Util.null2String(params.get("searchSort")); // 时间排序方式，0正序 1倒序
        String table_report = Util.null2String(params.get("table_report")); //建模表单名-案件报告
        String table_project = Util.null2String(params.get("table_project")); // 建模表单名-项目
        String sql = "select " +
                " a.id, " + // 数据id
                " a.ajxz as xmid," + // 项目id
                " b.ajmc as xmmc," + // 项目名称
                " b.lcbh as xmbh," + // 项目编号
                " b.kh as khid, " + // 客户id
                " crm.name as khname," + // 客户（名称）
                " crm.manager as manager, " + // 客户经理id
                " hrm.lastname as managername," + // 客户经理名称
                " b.xy as hycz," +//行业  行业-产轴id （10_1）
                " a.cppp," + //产品品牌 （多树）ids （19_7,19_17,19_18,19_13）
                " a.cpjl," + //产品经理  多人力ids
                " a.tbrq as rq, " + // 报告日期
                " a.tbr as tbr," + // 报告人id
                " hrm1.lastname as tbrname," + // 报告人姓名称
                " a.ajtdms," +//项目痛点描述 多行文本
                " a.gjxq," +//跟进详情 多行文本
                " a.xqjh, " + //下期计划 多行文本
                " a.fj  " + // 相关附件 ids
                " from " + table_report + "  a " +
                " left join " + table_project + " b on (a.ajxz = b.id) " +
                " left join crm_customerinfo crm on (b.kh = crm.id) " +
                " left join hrmresource hrm on (crm.manager = hrm.id) " +
                " left join hrmresource hrm1 on (a.tbr = hrm1.id) " +
                " where 1=1 ";
        sql += " and a.id in (" + batchAuthIds + ") ";

        // 排序
        if ("1".equals(searchSort)) {
            sql += " order by a.tbrq desc,a.tbsj desc,a.id desc ";
        } else {
            sql += " order by a.tbrq,a.tbsj,a.id ";
        }

        return sql;
    }


    /**
     * 详情描述数据
     *
     * @param authids
     * @return
     */
    private String getDetailcommentSql(String authids) {
        String report_modid = Util.null2String(params.get("report_modid")); // 报告的建模id
        String sql = "select " +
                " a.id, " + // 评论数据id
                " a.rqid, " + // 对应建模的数据id
                " a.replyor, " + // 评论人id
                " b.lastname as replyorname, " + // 评论人姓名
                " a.replydate, " + // 评论日期
                " a.replytime, " + // 评论时间
                " a.replycontent, " + // 评论内容 html格式
                " a.quotesid, " + // 引用评论的id
                " a.commentid, " + // 回复评论的id
                " a.commenttopid " + // 回复评论的顶级id
                " from uf_reply  a " +
                " left join hrmresource b on (b.id = a.replyor) " +
                " where 1=1 " +
                " and a.rqmodeid = " + report_modid;
        String quotedIds = "";
        if (authids != null && !authids.isEmpty()) {
            quotedIds = "'" + authids.replaceAll(",", "','") + "'";
        }
        sql += " and a.rqid in (" + quotedIds + ") "; // 建模的数据id
        sql += " order by a.rqid,a.replydate,a.replytime";
        return sql;
    }

    /**
     * 获取产品类别映射关系
     */
    private Map<String, String> getCPLBMap() {
        Map<String, String> result = new HashMap<>();
        String sql = "select * from uf_cplb ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            while (rs.next()) {
                result.put(rs.getString("id"), rs.getString("cplb"));
            }
        }
        return result;
    }


    /**
     * 获取行业-产轴的数据 数据list
     */
    private List<Map<String, Object>> getHangyeChanZhouList() {
        String sql = "select * from ygv_Trade_cz ";
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (rs.executeQuery(sql)) {
            return SDUtil.lowerMapKey(QueryUtil.getMapList(rs));
        }
        return null;
    }

    /**
     * 行业-产轴 字段处理
     */
    private void handleField_hycz() {
        //主键为fitemid，上级id为fparentid，显示名为fname，当fparentid=0时为顶级
        List<Map<String, Object>> list = getHangyeChanZhouList();
        log.info("getHangyeChanZhouList list: " + list);
        // 1. 构建id->节点的Map，方便查找
        Map<String, Map<String, Object>> idMap = new HashMap<>();
        if (list != null && !list.isEmpty()) {
            for (Map<String, Object> item : list) {
                idMap.put(Util.null2String(item.get("fitemid")), item);
            }
            for (Map<String, Object> data : reportData) {
                // 存的值：10_1
                String fieldValue = Util.null2String(data.get("hycz"));
                if (!fieldValue.isEmpty()) {
                    String id = fieldValue.trim().split("_")[1];
                    // 2. 递归查找全路径
                    List<String> path = new ArrayList<>();
                    String curId = id;
                    while (idMap.containsKey(curId)) {
                        Map<String, Object> node = idMap.get(curId);
                        path.add(0, Util.null2String(node.get("fname")));
                        String parentId = Util.null2String(node.get("fparentid"));
                        if ("0".equals(parentId) || parentId.isEmpty()) {
                            break;
                        }
                        curId = parentId;
                    }
                    data.put("hyczname", String.join("/", path));
                } else {
                    //如果字段为空，则赋值name为空
                    data.put("hyczname", "");
                }
            }
        }

    }

    /**
     * 字段处理
     * 产品经理
     */
    private void handleField_cpjl() {
        Set<String> allIds = new HashSet<>();
        for (Map<String, Object> data : reportData) {
            String cpjl = Util.null2String(data.get("cpjl"));
            if (!cpjl.isEmpty()) {
                allIds.addAll(Arrays.asList(cpjl.split(CommonCst.COMMA_EN)));
            }
        }
        //根据所有的人员id查询所有人员信息
        Map<String, String> hrmIdNameMap = HrmUtil.getHrmIdNameMap(allIds);
        for (Map<String, Object> data : reportData) {
            String cpjl = Util.null2String(data.get("cpjl"));
            List<String> names = new ArrayList<>();
            if (!cpjl.isEmpty()) {
                List<Map<String, Object>> list = new ArrayList<>();
                // 假设sjcp字段是以逗号分隔的产品ID
                String[] ids = cpjl.split(CommonCst.COMMA_EN);
                for (String eachId : ids) {
                    //人员id
                    if (!eachId.isEmpty()) {
                        Map<String, Object> idNameMap = new HashMap<>();
                        idNameMap.put("id", eachId);
                        // 从chanpinMap中获取产品类别名称
                        String name = Util.null2String(hrmIdNameMap.get(eachId));
                        idNameMap.put("name", name);
                        names.add(name);
                        list.add(idNameMap);
                    }
                }
                data.put("cpjlnames", String.join(",", names));
                data.put("cpjlList", list);
            } else {
                // 如果sjcp为空，设置为空列表
                data.put("cpjlnames", "");
                data.put("cpjlList", new ArrayList<>());
            }
        }
    }


    /**
     * 字段处理
     * 产品品牌
     */
    private void handleField_cppp() {
        //先获取产品类别的map
        Map<String, String> chanpinMap = getCPLBMap();
        log.info("chanpinMap: " + chanpinMap);
        if (!chanpinMap.isEmpty()) {
            for (Map<String, Object> data : reportData) {
                String sjcp = Util.null2String(data.get("cppp"));
                List<String> names = new ArrayList<>();
                if (!sjcp.isEmpty()) {
                    List<Map<String, Object>> sjcpList = new ArrayList<>();
                    // 假设sjcp字段是以逗号分隔的产品ID
                    String[] productIds = sjcp.split(CommonCst.COMMA_EN);
                    for (String productId : productIds) {
                        //取_后面的值为数据id，_前面的是树id
                        productId = productId.trim().split("_")[1];
                        if (!productId.isEmpty()) {
                            Map<String, Object> productMap = new HashMap<>();
                            productMap.put("id", productId);
                            // 从chanpinMap中获取产品类别名称
                            String categoryName = Util.null2String(chanpinMap.get(productId));
                            productMap.put("name", categoryName);
                            names.add(categoryName);
                            sjcpList.add(productMap);
                        }
                    }
                    data.put("cpppnames", String.join(",", names));
                    data.put("chanpinList", sjcpList);
                } else {
                    // 如果sjcp为空，设置为空列表
                    data.put("cpppnames", "");
                    data.put("chanpinList", new ArrayList<>());
                }
            }
        }

    }

    /**
     * 后置执行操作
     */
    private void afterExecute() {
        try {
            // 清除RecordSet
            DBUtil.clearThreadLocalRecordSet();
            // 插入二开日志
            if (sdLog != null) {
                String needlog = Util.null2String(params.get("needlog")); // 是否记录日志
                if ("1".equals(needlog)) {
                    // 异步插入日志
                    SDLog.saveLogAsync(sdLog, sdLogUtil.getFullLog(), error);
                }
            }
        } catch (Exception e) {
            log.error("后置动作执行异常：", e);
        }
    }

    /**
     * 校验参数
     *
     * @return
     */
    private void checkParam() {
        List<String> missedParams = new ArrayList<>();
        List<String> requiredParamsList = new ArrayList<>();
        requiredParamsList.add("port");
        requiredParamsList.add("auth_customid");
        requiredParamsList.add("report_modid");
        requiredParamsList.add("xm_paramname");
        requiredParamsList.add("bgr_paramname");
        requiredParamsList.add("bgrq_paramname");
        requiredParamsList.add("searchSort");
        requiredParamsList.add("pageNo");
        requiredParamsList.add("table_report");
        requiredParamsList.add("table_project");

        for (String param : requiredParamsList) {
            if (Util.null2String(params.get(param)).isEmpty()) {
                missedParams.add(param);
            }
        }
        if (!missedParams.isEmpty()) {
            error = "缺失必填参数:" + missedParams;
        }

    }

    /**
     * 添加日志
     *
     * @param logMsg
     */
    private void appendLog(String logMsg) {
        log.info(logMsg);
        sdLogUtil.appendLog(logMsg);
    }

}
